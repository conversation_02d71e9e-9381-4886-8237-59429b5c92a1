# Askelio Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# ===== SUPABASE CONFIGURATION =====
# New Supabase project: askelio-auth (nfmjqnojvjjapszgwcfd)
SUPABASE_URL=https://nfmjqnojvjjapszgwcfd.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# ===== DATABASE CONFIGURATION =====
# Primary database (Supabase PostgreSQL)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.nfmjqnojvjjapszgwcfd.supabase.co:5432/postgres

# Fallback SQLite for development (optional)
SQLITE_FILE=askelio_dev.db
SQL_DEBUG=false

# ===== API CONFIGURATION =====
API_HOST=0.0.0.0
API_PORT=8001
API_WORKERS=1
API_RELOAD=true

# CORS settings
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
CORS_CREDENTIALS=true

# ===== AUTHENTICATION =====
JWT_SECRET_KEY=your_super_secret_jwt_key_here_min_32_chars
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Session configuration
SESSION_SECRET_KEY=your_session_secret_key_here
SESSION_EXPIRE_HOURS=24

# ===== AI/LLM CONFIGURATION =====
# OpenRouter API for AI models
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Default AI models
DEFAULT_LLM_MODEL=anthropic/claude-3.5-sonnet
FALLBACK_LLM_MODEL=anthropic/claude-3-haiku
SPEED_LLM_MODEL=openai/gpt-4o

# Model costs (credits per 1K tokens)
CLAUDE_35_SONNET_COST=0.003
GPT_4O_COST=0.0025
CLAUDE_3_HAIKU_COST=0.0005

# ===== OCR CONFIGURATION =====
# Google Vision API
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
GOOGLE_VISION_API_KEY=your_google_vision_api_key

# Azure Computer Vision (optional)
AZURE_CV_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_CV_KEY=your_azure_cv_key

# Tesseract OCR (local fallback)
TESSERACT_CMD=tesseract
TESSERACT_DATA_PATH=/usr/share/tesseract-ocr/4.00/tessdata

# ===== FILE STORAGE =====
# Local storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,gif,bmp,tiff

# Cloud storage (optional)
# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=eu-central-1
AWS_S3_BUCKET=askelio-documents

# Google Cloud Storage (optional)
GCS_BUCKET_NAME=askelio-documents
GCS_PROJECT_ID=your_gcs_project_id

# ===== REDIS CONFIGURATION (for caching and queues) =====
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Cache settings
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# ===== EMAIL CONFIGURATION =====
# SMTP settings for notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_USE_TLS=true
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Askelio

# ===== PAYMENT CONFIGURATION =====
# Stripe for credit purchases
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Credit packages
CREDIT_PACKAGES=10:99,50:399,100:699,500:2999

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/askelio.log
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=5

# ===== MONITORING =====
# Sentry for error tracking (optional)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development

# Health check settings
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=10

# ===== RATE LIMITING =====
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# API rate limits by subscription tier
FREE_TIER_REQUESTS_PER_HOUR=100
BASIC_TIER_REQUESTS_PER_HOUR=1000
PREMIUM_TIER_REQUESTS_PER_HOUR=10000

# ===== DEVELOPMENT SETTINGS =====
DEBUG=true
TESTING=false
DEVELOPMENT=true

# Auto-reload settings
AUTO_RELOAD=true
RELOAD_DIRS=./,../frontend/src

# ===== SECURITY =====
# Security headers
SECURITY_HEADERS_ENABLED=true
HTTPS_ONLY=false
SECURE_COOKIES=false

# API security
API_KEY_HEADER=X-API-Key
REQUIRE_API_KEY=false

# ===== FEATURE FLAGS =====
ENABLE_USER_REGISTRATION=true
ENABLE_CREDIT_SYSTEM=true
ENABLE_MEMORY_SYSTEM=true
ENABLE_DOCUMENT_EXPIRATION=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# ===== ANALYTICS =====
# Google Analytics (optional)
GA_TRACKING_ID=G-XXXXXXXXXX

# Internal analytics
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

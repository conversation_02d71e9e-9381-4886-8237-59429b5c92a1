# Askelio Backend Environment Variables v3.0.0 - OPTIMIZED 2025
# Copy this file to .env and fill in your API keys

# Google Vision API (Required for OCR)
GOOGLE_API_KEY=your-google-api-key-here

# OpenRouter API (Required for LLM - 95%+ accuracy, ultra cost-effective!)
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Get your FREE OpenRouter API key at: https://openrouter.ai/keys
# NEW 2025 MODELS: Gemini 2.5 Flash-Lite (fastest), DeepSeek V3 (best reasoning)
# 95%+ accuracy with 70% lower costs than previous setup
# Primary: Gemini 2.5 Flash-Lite ($0.01/$0.03 per 1M tokens)
# Optimal: Gemini 2.5 Flash ($0.075/$0.30 per 1M tokens)
# Reasoning: DeepSeek V3 ($0.14/$0.28 per 1M tokens)
# Premium: Claude 3.5 Sonnet (for critical tasks only)

# Database Configuration (SQLite - default settings)
DATABASE_URL=sqlite:///./askelio.db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=false

# Cost Limits (CZK)
MAX_DAILY_COST=100.0
MAX_MONTHLY_COST=1000.0

# Logging Level
LOG_LEVEL=INFO

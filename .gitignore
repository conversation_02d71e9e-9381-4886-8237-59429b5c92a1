# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Google Cloud credentials (keep template, ignore actual credentials)
backend/google-credentials.json

# Build outputs
/frontend/.next/
/frontend/out/
/frontend/build/
/backend/dist/
*.egg-info/

# Database
*.db
*.sqlite
*.sqlite3

# Uploads and temp files
uploads/
temp/
tmp/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Pytest
.pytest_cache/

# Celery
celerybeat-schedule
celerybeat.pid

# Redis dump
dump.rdb

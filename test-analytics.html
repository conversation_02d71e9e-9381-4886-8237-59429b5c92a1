<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Analytics Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #2563eb;
        }
        .metric-label {
            color: #6b7280;
            margin-top: 5px;
        }
        .charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        .chart-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        .error {
            background: #fee2e2;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #dcfce7;
            color: #16a34a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Test Analytics Dashboard</h1>
        <div id="status" class="loading">Načítám analytická data...</div>
        
        <div id="metrics" class="metrics" style="display: none;">
            <div class="metric-card">
                <div id="income" class="metric-value">0</div>
                <div class="metric-label">Celkové příjmy (CZK)</div>
            </div>
            <div class="metric-card">
                <div id="expenses" class="metric-value">0</div>
                <div class="metric-label">Celkové výdaje (CZK)</div>
            </div>
            <div class="metric-card">
                <div id="profit" class="metric-value">0</div>
                <div class="metric-label">Čistý zisk (CZK)</div>
            </div>
            <div class="metric-card">
                <div id="margin" class="metric-value">0%</div>
                <div class="metric-label">Zisková marže</div>
            </div>
            <div class="metric-card">
                <div id="documents" class="metric-value">0</div>
                <div class="metric-label">Dokumenty zpracované</div>
            </div>
            <div class="metric-card">
                <div id="users" class="metric-value">0</div>
                <div class="metric-label">Aktivní uživatelé</div>
            </div>
        </div>

        <div id="charts" class="charts" style="display: none;">
            <div class="chart-container">
                <h3>Měsíční trendy</h3>
                <canvas id="monthlyChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
                <h3>Kategorie výdajů</h3>
                <canvas id="expenseChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Mock analytics data (same as in dashboard-api.ts)
        const mockAnalyticsData = {
            success: true,
            data: {
                overview: {
                    total_income: 1250000,
                    total_expenses: 850000,
                    net_profit: 400000,
                    documents_this_period: 47,
                    pending_approvals: 8,
                    active_users: 12,
                    total_storage_gb: 15.7,
                    profit_margin: 32.0
                },
                trends: {
                    monthly_data: [
                        { month: 'Led', income: 180000, expenses: 120000, profit: 60000, documents: 23 },
                        { month: 'Úno', income: 220000, expenses: 140000, profit: 80000, documents: 28 },
                        { month: 'Bře', income: 190000, expenses: 130000, profit: 60000, documents: 25 },
                        { month: 'Dub', income: 240000, expenses: 150000, profit: 90000, documents: 31 },
                        { month: 'Kvě', income: 260000, expenses: 160000, profit: 100000, documents: 35 },
                        { month: 'Čer', income: 245000, expenses: 156000, profit: 89000, documents: 33 }
                    ],
                    expense_categories: [
                        { category: 'Služby', amount: 385000, percentage: 45.3, color: '#3b82f6' },
                        { category: 'Materiál', amount: 255000, percentage: 30.0, color: '#10b981' },
                        { category: 'Energie', amount: 127500, percentage: 15.0, color: '#f59e0b' },
                        { category: 'Ostatní', amount: 82500, percentage: 9.7, color: '#ef4444' }
                    ]
                }
            }
        };

        function formatNumber(num) {
            return new Intl.NumberFormat('cs-CZ').format(num);
        }

        function loadAnalytics() {
            try {
                // Simulate API call delay
                setTimeout(() => {
                    const data = mockAnalyticsData.data;
                    
                    // Update status
                    document.getElementById('status').innerHTML = '<div class="success">✅ Analytická data úspěšně načtena (mock data pro testování)</div>';
                    
                    // Show metrics
                    document.getElementById('metrics').style.display = 'grid';
                    document.getElementById('charts').style.display = 'grid';
                    
                    // Update metrics
                    document.getElementById('income').textContent = formatNumber(data.overview.total_income);
                    document.getElementById('expenses').textContent = formatNumber(data.overview.total_expenses);
                    document.getElementById('profit').textContent = formatNumber(data.overview.net_profit);
                    document.getElementById('margin').textContent = data.overview.profit_margin.toFixed(1) + '%';
                    document.getElementById('documents').textContent = data.overview.documents_this_period;
                    document.getElementById('users').textContent = data.overview.active_users;
                    
                    // Create charts
                    createMonthlyChart(data.trends.monthly_data);
                    createExpenseChart(data.trends.expense_categories);
                    
                }, 1000);
                
            } catch (error) {
                document.getElementById('status').innerHTML = '<div class="error">❌ Chyba při načítání dat: ' + error.message + '</div>';
            }
        }

        function createMonthlyChart(monthlyData) {
            const ctx = document.getElementById('monthlyChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthlyData.map(d => d.month),
                    datasets: [
                        {
                            label: 'Příjmy',
                            data: monthlyData.map(d => d.income),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Výdaje',
                            data: monthlyData.map(d => d.expenses),
                            borderColor: '#ef4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Zisk',
                            data: monthlyData.map(d => d.profit),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value) + ' CZK';
                                }
                            }
                        }
                    }
                }
            });
        }

        function createExpenseChart(expenseData) {
            const ctx = document.getElementById('expenseChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: expenseData.map(d => d.category),
                    datasets: [{
                        data: expenseData.map(d => d.percentage),
                        backgroundColor: expenseData.map(d => d.color),
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Load analytics on page load
        window.addEventListener('load', loadAnalytics);
    </script>
</body>
</html>

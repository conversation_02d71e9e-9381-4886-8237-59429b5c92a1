#!/usr/bin/env python3
"""
Test script to reproduce the document processing error
"""
import sys
import os
sys.path.append('backend')

from unified_document_processor import UnifiedDocumentProcessor, ProcessingOptions, ProcessingMode
import tempfile

def test_document_processing():
    """Test document processing to reproduce the error"""
    
    # Create a test file
    test_content = """
    FAKTURA
    Číslo faktury: 2024001
    Datum: 15.01.2024
    Částka: 1500 Kč
    Dodavatel: Test s.r.o.
    """
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file = f.name
    
    try:
        # Create processor
        print("🔧 Creating UnifiedDocumentProcessor...")
        processor = UnifiedDocumentProcessor()
        print("✅ Processor created successfully")
        
        # Create options with invalid user_id to trigger database error
        options = ProcessingOptions(
            mode=ProcessingMode.COST_OPTIMIZED,
            store_in_db=True,
            user_id="invalid-user-id-that-should-cause-error",
            return_raw_text=True
        )
        
        # Process document
        print("🚀 Starting document processing...")
        result = processor.process_document(temp_file, "test_invoice.txt", options)
        
        print(f"✅ Processing result: {result}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if os.path.exists(temp_file):
            os.unlink(temp_file)

# Commented out to avoid conflicts
# if __name__ == "__main__":
#     test_document_processing()

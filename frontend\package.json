{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:production": "node scripts/build-production.js", "start": "next start", "lint": "next lint", "test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:dashboard": "node scripts/test-dashboard.js", "test:dashboard:basic": "playwright test dashboard.spec.ts", "test:dashboard:interactions": "playwright test dashboard-interactions.spec.ts", "test:dashboard:visual": "playwright test dashboard-visual.spec.ts --project=visual-tests", "test:dashboard:mobile": "playwright test dashboard*.spec.ts --project=\"Mobile Chrome\"", "test:dashboard:tablet": "playwright test dashboard*.spec.ts --project=Tablet", "test:dashboard:desktop": "playwright test dashboard*.spec.ts --project=chromium", "test:dashboard:all": "playwright test dashboard*.spec.ts", "test:update-snapshots": "playwright test --update-snapshots"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.51.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "embla-carousel-react": "^8.6.0", "geist": "^1.4.2", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "^15.2.4", "next-themes": "^0.2.1", "react": "19.1.0", "react-day-picker": "^8.10.1", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}
/**
 * Utility functions for formatting data in the Askelio application
 */

/**
 * Format amount for display, handling various input formats
 * @param amount - The amount to format (can be number, string, or object)
 * @param currency - The currency code (default: 'CZK')
 * @returns Formatted amount string
 */
export function formatAmount(amount: any, currency: string = 'CZK'): string {
  if (amount === null || amount === undefined) return 'N/A'
  
  // Handle different amount formats
  if (typeof amount === 'object') {
    // If amount is an object, try to extract the numeric value
    if (amount.value !== undefined) return formatNumber(amount.value, currency)
    if (amount.amount !== undefined) return formatNumber(amount.amount, currency)
    if (amount.total !== undefined) return formatNumber(amount.total, currency)
    // If it's an object but no recognizable fields, return N/A
    return 'N/A'
  }
  
  return formatNumber(amount, currency)
}

/**
 * Format a numeric value as currency
 * @param value - The numeric value to format
 * @param currency - The currency code (default: 'CZK')
 * @returns Formatted currency string
 */
export function formatNumber(value: any, currency: string = 'CZK'): string {
  const num = parseFloat(value)
  if (isNaN(num)) return 'N/A'
  
  return new Intl.NumberFormat('cs-CZ', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num)
}

/**
 * Format a simple number without currency symbol
 * @param value - The numeric value to format
 * @returns Formatted number string
 */
export function formatSimpleNumber(value: any): string {
  const num = parseFloat(value)
  if (isNaN(num)) return 'N/A'
  
  return new Intl.NumberFormat('cs-CZ', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num)
}

/**
 * Format date for display
 * @param date - The date to format (string or Date object)
 * @returns Formatted date string
 */
export function formatDate(date: any): string {
  if (!date) return 'N/A'
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (isNaN(dateObj.getTime())) return 'N/A'
    
    return new Intl.DateTimeFormat('cs-CZ', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(dateObj)
  } catch (error) {
    return 'N/A'
  }
}

/**
 * Format confidence score as percentage
 * @param confidence - The confidence score (0-1)
 * @returns Formatted percentage string
 */
export function formatConfidence(confidence: any): string {
  const num = parseFloat(confidence)
  if (isNaN(num)) return 'N/A'
  
  return new Intl.NumberFormat('cs-CZ', {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  }).format(num)
}

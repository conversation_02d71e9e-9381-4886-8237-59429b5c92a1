"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, Clock, Target, CreditCard, TrendingUp, TrendingDown, DollarSign, PieChart, BarChart3, MessageSquare } from "lucide-react"
// Přidaj import pro ExportDialog
import { ExportDialog } from "@/components/export-dialog"
import { Button } from "@/components/ui/button"
import { Download } from "lucide-react"
import { useEffect, useState } from "react"
import { apiClient } from "@/lib/api"

interface FinancialData {
  totalRevenue: number
  totalExpenses: number
  monthlyRevenue: number
  monthlyExpenses: number
  topVendors: Array<{
    name: string
    amount: number
    count: number
  }>
  revenueByMonth: Array<{
    month: string
    revenue: number
    expenses: number
  }>
  vatSummary: {
    totalVat: number
    vatByRate: Array<{
      rate: number
      amount: number
    }>
  }
}

interface StatsData {
  processedDocuments: number
  timeSaved: number
  accuracy: number
  remainingCredits: number
  financialData: FinancialData
  trends: {
    documents: number
    timeSaved: number
    accuracy: number
    credits: number
    revenue: number
    expenses: number
  }
}

export function StatsCards() {
  const [stats, setStats] = useState<StatsData>({
    processedDocuments: 0,
    timeSaved: 0,
    accuracy: 0,
    remainingCredits: 0,
    trends: {
      documents: 0,
      timeSaved: 0,
      accuracy: 0,
      credits: 0
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch documents and credits from API
        console.log('🚀 StatsCards: Fetching stats using API client...')
        const [documents, credits] = await Promise.all([
          apiClient.getDocuments(),
          apiClient.getCreditBalance()
        ])

        console.log('📄 StatsCards: Documents:', documents)
        console.log('💳 StatsCards: Credits:', credits)

        // Calculate stats from real data
        const processedDocs = documents.length
        const completedDocs = documents.filter((doc: any) => doc.status === 'completed').length
        const avgAccuracy = completedDocs > 0
          ? documents
              .filter((doc: any) => doc.status === 'completed' && doc.accuracy)
              .reduce((sum: number, doc: any) => sum + doc.accuracy, 0) / completedDocs
          : 0

        setStats({
          processedDocuments: processedDocs,
          timeSaved: processedDocs * 0.5, // Estimate 30 minutes saved per document
          accuracy: avgAccuracy,
          remainingCredits: credits,
          trends: {
            documents: 12.5,
            timeSaved: 8.1,
            accuracy: 0.3,
            credits: -15.2
          }
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
        // Show error state instead of fake data
        setStats({
          processedDocuments: 0,
          timeSaved: 0,
          accuracy: 0,
          remainingCredits: 0,
          trends: {
            documents: 0,
            timeSaved: 0,
            accuracy: 0,
            credits: 0
          }
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statsCards = [
    {
      title: "Zpracované dokumenty",
      value: loading ? "..." : stats.processedDocuments.toLocaleString(),
      change: `+${stats.trends.documents}%`,
      trend: "up",
      icon: FileText,
      description: "Tento měsíc",
    },
    {
      title: "Úspora času",
      value: loading ? "..." : `${stats.timeSaved.toFixed(1)}h`,
      change: `+${stats.trends.timeSaved}%`,
      trend: "up",
      icon: Clock,
      description: "Celkem ušetřeno",
    },
    {
      title: "Přesnost OCR",
      value: loading ? "..." : `${stats.accuracy.toFixed(1)}%`,
      change: `+${stats.trends.accuracy}%`,
      trend: "up",
      icon: Target,
      description: "Průměrná přesnost",
    },
    {
      title: "Zbývající kredity",
      value: loading ? "..." : stats.remainingCredits.toLocaleString(),
      change: `${stats.trends.credits}%`,
      trend: "down",
      icon: CreditCard,
      description: "Z 5,000 kreditů",
    },
  ]
  return (
    <>
      {/* statistic cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant={stat.trend === "up" ? "default" : "destructive"} className="text-xs">
                  {stat.trend === "up" ? (
                    <TrendingUp className="w-3 h-3 mr-1" />
                  ) : (
                    <TrendingDown className="w-3 h-3 mr-1" />
                  )}
                  {stat.change}
                </Badge>
                <span className="text-xs text-gray-500 dark:text-gray-400">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* floating quick-export button */}
      <div className="fixed bottom-6 right-6 z-50">
        <ExportDialog
          data={{
            statistics: {
              processedDocuments: stats.processedDocuments,
              timeSaved: stats.timeSaved,
              accuracy: stats.accuracy,
              remainingCredits: stats.remainingCredits,
            },
          }}
          trigger={
            <Button size="lg" className="rounded-full shadow-lg">
              <Download className="w-5 h-5 mr-2" />
              Rychlý export
            </Button>
          }
        />
      </div>
    </>
  )
}

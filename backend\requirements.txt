# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Pydantic
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Background tasks
celery==5.3.4
redis==5.0.1

# Core OCR and Image Processing
pillow==10.1.0
pytesseract==0.3.10
pdf2image==1.16.3
numpy==1.24.3

# OCR Providers - 5 Sources for Maximum Accuracy
# 1. Google Vision API (cloud-based, high accuracy)
google-cloud-vision==3.4.5
google-auth==2.23.4

# 2. Azure Computer Vision (cloud-based alternative)
azure-cognitiveservices-vision-computervision==0.9.0
msrest==0.7.1

# 3. Tesseract (open-source, local) - already included via pytesseract

# 4. EasyOCR (ML-based, local)
easyocr==1.7.0

# 5. PaddleOCR (ML-based, local, good for multiple languages)
paddlepaddle==2.5.2
paddleocr==2.7.3

# Google Gemini AI for decision making (legacy support)
google-generativeai==0.3.2

# OpenRouter API - Unified LLM Access (90% FREE processing)
requests==2.31.0             # For OpenRouter API calls

# Payment and External Services
stripe==7.8.0
supabase==2.0.2

# Environment and configuration
python-dotenv==1.0.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Google Cloud Services
google-cloud-storage==2.10.0
google-cloud-secret-manager==2.16.4
google-cloud-logging==3.8.0

# Utilities
python-dateutil==2.8.2

# Production monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Security
cryptography==41.0.8
